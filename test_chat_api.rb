#!/usr/bin/env ruby

require 'net/http'
require 'json'
require 'uri'

# Test the chat API to verify JSON format
def test_chat_api
  # First, let's test if we can access the API
  uri = URI('http://localhost:3000/api/v1/chat/users')
  
  begin
    response = Net::HTTP.get_response(uri)
    puts "Status: #{response.code}"
    puts "Response: #{response.body}"
    
    if response.code == '200'
      data = JSON.parse(response.body)
      puts "Users API working: #{data['users']&.length || 0} users found"
    else
      puts "API returned error: #{response.code}"
    end
  rescue => e
    puts "Error testing API: #{e.message}"
  end
end

test_chat_api
